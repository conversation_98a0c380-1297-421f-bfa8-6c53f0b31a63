import PlayButton from "@/components/play-button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { Episode } from "@/lib/data";
import { Calendar, Clock, Eye } from "lucide-react";
import Link from "next/link";

interface LatestEpisodeProps {
   episode: Episode | null;
}

export default function LatestEpisode({ episode }: LatestEpisodeProps) {
   if (!episode) {
      return (
         <section className="bg-gradient-to-br from-red-600 via-red-500 to-red-700 px-4 py-16 lg:py-24">
            <div className="mx-auto max-w-7xl text-center">
               <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4">
                  EPISODES
               </h1>
               <p className="text-xl text-white/90">
                  Loading latest episode...
               </p>
            </div>
         </section>
      );
   }

   return (
      <section className="bg-gradient-to-br from-red-600 via-red-500 to-red-700 px-4 py-16 lg:py-24 relative overflow-hidden">
         {/* Background Pattern */}
         <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-32 h-32 bg-yellow-400 rounded-full"></div>
            <div className="absolute top-32 right-20 w-24 h-24 bg-yellow-400 rounded-full"></div>
            <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-yellow-400 rounded-full"></div>
            <div className="absolute bottom-32 right-1/3 w-20 h-20 bg-yellow-400 rounded-full"></div>
         </div>

         <div className="mx-auto max-w-7xl relative z-10">
            {/* <div className="text-center mb-8">
          <Badge className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-4 py-2 text-sm mb-4">
            🔥 LATEST EPISODE
          </Badge>
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4">EPISODES</h1>
          <p className="text-xl text-white/90 max-w-2xl mx-auto">
            Discover the latest culinary adventures and catch up on all your favorite episodes
          </p>
        </div> */}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mt-12">
               {/* Episode Info */}
               <div className="space-y-6">
                  {episode.isNew && (
                     <Badge className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-3 py-1">
                        ✨ NEW
                     </Badge>
                  )}

                  <h2 className="text-3xl lg:text-4xl font-bold text-white leading-tight">
                     {episode.title}
                  </h2>

                  <p className="text-white/90 text-lg leading-relaxed">
                     {episode.description}
                  </p>

                  <div className="flex flex-wrap gap-6 text-white/80">
                     <div className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        <span className="font-medium">{episode.date}</span>
                     </div>
                     <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        <span className="font-medium">{episode.duration}</span>
                     </div>
                     <div className="flex items-center gap-2">
                        <Eye className="h-5 w-5" />
                        <span className="font-medium">
                           {episode.views} views
                        </span>
                     </div>
                  </div>

                  <div className="pt-2">
                     <p className="text-white/80 mb-4">
                        <span className="font-semibold">Featured Guest:</span>{" "}
                        {episode.guest}
                     </p>

                     <div className="flex flex-col sm:flex-row gap-4">
                        <Button
                           size="lg"
                           className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-8 py-4 text-lg rounded-full shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center"
                           asChild
                        >
                           <Link href={`/episodes/${episode.slug}`}>
                              WATCH FULL EPISODE
                           </Link>
                        </Button>
                        <Button
                           size="lg"
                           variant="outline"
                           className="border-2 border-white text-white hover:bg-white hover:text-red-600 bg-transparent px-8 py-4 text-lg rounded-full font-bold"
                        >
                           ADD TO WATCHLIST
                        </Button>
                     </div>
                  </div>
               </div>

               {/* Video Thumbnail */}
               <div className="relative">
                  <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl shadow-2xl overflow-hidden relative group">
                     {/* Play Button */}
                     <PlayButton
                        videoId={episode.videoId}
                        title={episode.title}
                        size="lg"
                        className="absolute-center transform group-hover:scale-110 transition-all duration-300"
                        variant="primary"
                     />

                     {/* Duration Badge */}
                     <div className="absolute bottom-4 right-4 bg-black/80 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {episode.duration}
                     </div>

                     {/* Gradient Overlay */}
                     <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  </div>
               </div>
            </div>
         </div>
      </section>
   );
}
