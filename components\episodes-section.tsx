"use client";

import PlayButton from "@/components/play-button";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useMobile } from "@/hooks/use-mobile";
import type { Episode } from "@/lib/data";
import { MoveRight, Plus } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { EpisodeCard } from "./episodes/episode-card";

interface EpisodesSectionProps {
   episodes: Episode[];
}

// Placeholder episode data
const createPlaceholderEpisode = (index: number): Episode => ({
   id: -index,
   title: "Coming Soon",
   guest: "New Guest",
   description: "Exciting new episode coming soon",
   date: "Coming Soon",
   duration: "TBD",
   views: "0",
   category: "Upcoming",
   slug: "coming-soon",
   videoId: "", // Empty videoId for placeholder
});

export default function EpisodesSection({ episodes }: EpisodesSectionProps) {
   const isMobile = useMobile();

   // Create display episodes with placeholders if needed (only on desktop)
   const displayEpisodes = () => {
      const targetCount = 3;
      const actualEpisodes = episodes.slice(0, targetCount);

      if (isMobile || actualEpisodes.length >= targetCount) {
         return actualEpisodes;
      }

      // Add placeholders for desktop
      const placeholdersNeeded = targetCount - actualEpisodes.length;
      const placeholders = Array.from({ length: placeholdersNeeded }, (_, i) =>
         createPlaceholderEpisode(i + 1)
      );

      return [...actualEpisodes, ...placeholders];
   };

   return (
      // <section className="bg-yellow-400 px-4 sm:px-8 py-12 lg:py-20">
      <section className="bg-gradient-to-r from-red-50 to-yellow-50 px-4 sm:px-8 py-12 lg:py-20">
         <div className="mx-auto max-w-7xl">
            {/* Header */}
            <div className="flex items-start justify-between mb-8 lg:mb-12">
               <div className="space-y-2">
                  <h2 className="text-xl lg:text-2xl xl:text-3xl font-extrabold text-gray-900">
                     LATEST EPISODES
                  </h2>
                  <p className="text-lg text-gray-600 max-w-2xl">
                     Discover engaging conversations and expert insights from
                     the food industry's most influential voices
                  </p>
               </div>
               <Button
                  variant="outline"
                  className="border-2 border-red-600 bg-transparent hover:bg-red-600 text-red-600 hover:text-white px-6 py-2 rounded-md font-bold"
                  asChild
               >
                  <Link href="/episodes">VIEW ALL</Link>
               </Button>
            </div>

            {/* Episodes Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
               {displayEpisodes().map((episode) => (
                  <EpisodeCard key={episode.id} episode={episode} />
               ))}
            </div>
         </div>
      </section>
   );
}
