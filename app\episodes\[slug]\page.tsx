import type { Metadata } from "next"
import { notFound } from "next/navigation"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import YouTubePlayer from "@/components/youtube-player"
import { Calendar, Clock, Share2, ThumbsUp, MessageSquare, Bookmark } from "lucide-react"
import { getEpisodeBySlug, getGuestByEpisodeId, getRelatedEpisodes } from "@/lib/data"
import Link from "next/link"

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const episode = await getEpisodeBySlug(params.slug)

  if (!episode) {
    return {
      title: "Episode Not Found | StarBite with Kat<PERSON>",
      description: "The requested episode could not be found.",
    }
  }

  return {
    title: `${episode.title} | StarBite with Katarina`,
    description: episode.description,
    keywords: `Nigerian cuisine, ${episode.category}, ${episode.guest}, food review, celebrity talk show, Katarina`,
  }
}

export default async function EpisodePage({ params }: { params: { slug: string } }) {
  const episode = await getEpisodeBySlug(params.slug)

  if (!episode) {
    notFound()
  }

  const [guest, relatedEpisodes] = await Promise.all([
    getGuestByEpisodeId(episode.id),
    getRelatedEpisodes(episode.id, episode.category),
  ])

  return (
    <main className="min-h-screen">
      <Header currentPage="Episode" />

      {/* Video Player Section */}
      <section className="bg-slate-900 px-4 py-12">
        <div className="mx-auto max-w-6xl">
          {/* Video Player */}
          <YouTubePlayer videoId={episode.videoId} title={episode.title} className="mb-6 rounded-lg shadow-2xl" />

          {/* Video Info */}
          <div className="space-y-4">
            <h1 className="text-2xl lg:text-4xl font-bold text-white">{episode.title}</h1>

            <div className="flex flex-wrap gap-4 text-white/80">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{episode.date}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{episode.duration}</span>
              </div>
              <div className="flex items-center gap-1">
                <span>{episode.views} views</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 pt-2">
              <Button className="bg-red-600 hover:bg-red-700 text-white">
                <ThumbsUp className="h-4 w-4 mr-2" />
                Like
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10 bg-transparent">
                <MessageSquare className="h-4 w-4 mr-2" />
                Comment
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10 bg-transparent">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10 bg-transparent">
                <Bookmark className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Episode Content */}
      <section className="bg-white px-4 py-12">
        <div className="mx-auto max-w-6xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <div className="prose prose-lg max-w-none">
                <h2 className="text-2xl font-bold text-slate-800">Episode Description</h2>
                <p>{episode.description}</p>

                {/* Episode Highlights */}
                <h3 className="text-xl font-bold text-slate-800 mt-8">Episode Highlights</h3>
                <ul>
                  <li>Exploring the bustling Makoko Market and its unique food offerings</li>
                  <li>Learning the secret behind the perfect suya spice blend</li>
                  <li>Meeting local food vendors and hearing their stories</li>
                  <li>Chef Tunde's demonstration of modern street food innovations</li>
                  <li>Tasting competition: Katarina vs. Chef Tunde on identifying regional spices</li>
                </ul>

                {/* Featured Dishes */}
                <h3 className="text-xl font-bold text-slate-800 mt-8">Featured Dishes</h3>
                <p>In this episode, we explore several iconic Nigerian street foods, including:</p>
                <ul>
                  <li>
                    <strong>Suya:</strong> Spicy skewered meat with yaji spice
                  </li>
                  <li>
                    <strong>Akara:</strong> Deep-fried bean cakes
                  </li>
                  <li>
                    <strong>Puff Puff:</strong> Sweet fried dough balls
                  </li>
                  <li>
                    <strong>Boli:</strong> Roasted plantains
                  </li>
                  <li>
                    <strong>Kilishi:</strong> Nigerian beef jerky
                  </li>
                </ul>

                <h3 className="text-xl font-bold text-slate-800 mt-8">Featured Guest</h3>
                {guest ? (
                  <div className="bg-slate-50 p-6 rounded-lg">
                    <h4 className="font-bold text-lg">{guest.name}</h4>
                    <p className="text-sm text-slate-600 mb-2">{guest.role}</p>
                    <p>{guest.bio}</p>
                  </div>
                ) : (
                  <p>Guest information coming soon.</p>
                )}
              </div>

              {/* Comments Section */}
              <div className="mt-12 pt-8 border-t border-slate-200">
                <h3 className="text-xl font-bold text-slate-800 mb-6">Comments</h3>

                {/* Comment Form */}
                <div className="mb-8">
                  <textarea
                    placeholder="Add a comment..."
                    className="w-full p-4 border border-slate-300 rounded-lg"
                    rows={3}
                  ></textarea>
                  <div className="mt-2 flex justify-end">
                    <Button className="bg-red-600 hover:bg-red-700 text-white">Post Comment</Button>
                  </div>
                </div>

                {/* Sample Comments */}
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="py-4 border-b border-slate-200 last:border-0">
                    <div className="flex gap-4">
                      <div className="w-10 h-10 rounded-full bg-gray-300 flex-shrink-0"></div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <h4 className="font-bold text-slate-800">User Name</h4>
                          <span className="text-xs text-slate-500">2 days ago</span>
                        </div>
                        <p className="text-slate-600 mt-1">
                          This episode was amazing! I've been trying to perfect my Jollof rice for years, and these tips
                          are exactly what I needed.
                        </p>
                        <div className="flex gap-4 mt-2">
                          <Button variant="ghost" size="sm" className="h-8 px-2 text-slate-500">
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            Like (24)
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 px-2 text-slate-500">
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <Button variant="outline" className="w-full mt-4 bg-transparent">
                  Load More Comments
                </Button>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Guest Info */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg text-slate-800 mb-4">Featured Guest</h3>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-24 h-24 rounded-full bg-gray-300 mb-4">{/* Guest image placeholder */}</div>
                    <h4 className="font-bold text-slate-800">{episode.guest.name}</h4>
                    <p className="text-slate-600 text-sm mb-2">{episode.guest.role}</p>
                    <p className="text-slate-600 text-sm">{episode.guest.bio}</p>
                  </div>
                </CardContent>
              </Card>


              {/* Related Episodes */}
              <div>
                <h3 className="font-bold text-lg text-slate-800 mb-4">Related Episodes</h3>
                <div className="space-y-4">
                  {relatedEpisodes.length > 0 ? (
                    relatedEpisodes.map((relatedEpisode) => (
                      <Card key={relatedEpisode.id} className="overflow-hidden">
                        <div className="aspect-video bg-gray-300 relative">
                          <Link
                            href={`/episodes/${relatedEpisode.slug}`}
                            className="absolute inset-0 flex items-center justify-center"
                          >
                            <Button
                              size="sm"
                              variant="secondary"
                              className="bg-red-600 hover:bg-red-700 text-white rounded-full p-2"
                            >
                              <ThumbsUp className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                        <CardContent className="p-4">
                          <h4 className="font-bold text-slate-800 line-clamp-1">
                            <Link href={`/episodes/${relatedEpisode.slug}`} className="hover:text-red-600">
                              {relatedEpisode.title}
                            </Link>
                          </h4>
                          <p className="text-sm text-slate-600">{relatedEpisode.guest}</p>
                          <p className="text-xs text-slate-500">{relatedEpisode.duration}</p>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <p className="text-slate-600">No related episodes found.</p>
                  )}
                </div>
              </div>

              {/* Newsletter */}
              <Card className="bg-yellow-400">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg text-slate-800 mb-2">Never Miss an Episode</h3>
                  <p className="text-slate-700 text-sm mb-4">
                    Subscribe to get notifications when new episodes are released.
                  </p>
                  <input
                    type="email"
                    placeholder="Your email"
                    className="w-full px-3 py-2 rounded-md text-slate-800 mb-2"
                  />
                  <Button className="w-full bg-red-600 hover:bg-red-700 text-white">Subscribe</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
