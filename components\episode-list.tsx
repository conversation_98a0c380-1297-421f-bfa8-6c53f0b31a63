"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Calendar, Star, TrendingUp, Search } from "lucide-react"
import Link from "next/link"
import EpisodeSearch from "./episode-search"
import type { Episode } from "@/lib/data"

const categoryColors = {
  "Street Food": "bg-red-500 text-white",
  Traditional: "bg-yellow-500 text-black",
  Fusion: "bg-purple-500 text-white",
  Markets: "bg-green-500 text-white",
  Challenge: "bg-orange-500 text-white",
  Breakfast: "bg-blue-500 text-white",
  Ingredients: "bg-pink-500 text-white",
  Culture: "bg-indigo-500 text-white",
  Desserts: "bg-rose-500 text-white",
  Seafood: "bg-teal-500 text-white",
  Snacks: "bg-amber-500 text-black",
}

interface EpisodeListProps {
  episodes: Episode[]
}

export default function EpisodeList({ episodes }: EpisodeListProps) {
  const [searchQuery, setSearchQuery] = useState("")

  const filteredEpisodes = useMemo(() => {
    if (!searchQuery.trim()) return episodes

    return episodes.filter(
      (episode) =>
        episode.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        episode.guest.toLowerCase().includes(searchQuery.toLowerCase()) ||
        episode.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        episode.category.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }, [episodes, searchQuery])

  return (
    <>
      <EpisodeSearch onSearch={setSearchQuery} />

      <section className="bg-slate-50 px-4 py-16 lg:py-24">
        <div className="mx-auto max-w-7xl">
          {/* Results Header */}
          <div className="mb-12">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-2">
                  {searchQuery ? `Search Results` : `All Episodes`}
                </h2>
                <p className="text-slate-600">
                  {searchQuery
                    ? `Found ${filteredEpisodes.length} episode${filteredEpisodes.length !== 1 ? "s" : ""} matching "${searchQuery}"`
                    : `${episodes.length} episodes available`}
                </p>
              </div>

              {!searchQuery && (
                <div className="text-right">
                  <p className="text-slate-600 text-sm">New episodes every Friday</p>
                  <p className="text-slate-800 font-semibold">Next episode: July 27, 2025</p>
                </div>
              )}
            </div>
          </div>

          {/* Episodes Grid */}
          {filteredEpisodes.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredEpisodes.map((episode) => (
                <Card
                  key={episode.id}
                  className="group overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 bg-white"
                >
                  <div className="relative">
                    {/* Video Thumbnail */}
                    <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 relative overflow-hidden">
                      {/* Play Button Overlay */}
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20 group-hover:bg-black/40 transition-all duration-300">
                        <Button
                          size="lg"
                          className="bg-red-600 hover:bg-red-700 text-white rounded-full p-4 shadow-lg transform group-hover:scale-110 transition-all duration-300"
                        >
                          <Play className="h-8 w-8 fill-current" />
                        </Button>
                      </div>

                      {/* Duration Badge */}
                      <div className="absolute bottom-3 right-3 bg-black/80 text-white px-2 py-1 rounded text-sm font-medium">
                        {episode.duration}
                      </div>

                      {/* Popular Badge */}
                      {episode.isPopular && (
                        <div className="absolute top-3 left-3 bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                          <TrendingUp className="h-3 w-3" />
                          POPULAR
                        </div>
                      )}

                      {/* Latest Badge */}
                      {episode.isLatest && (
                        <div className="absolute top-3 right-3 bg-red-600 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
                          <Star className="h-3 w-3 fill-current" />
                          NEW
                        </div>
                      )}
                    </div>

                    {/* Episode Info */}
                    <div className="p-6 space-y-4">
                      {/* Category Badge */}
                      <Badge
                        className={`${categoryColors[episode.category as keyof typeof categoryColors]} text-xs font-bold px-2 py-1`}
                      >
                        {episode.category}
                      </Badge>

                      {/* Title */}
                      <h3 className="font-bold text-xl text-slate-800 line-clamp-2 group-hover:text-red-600 transition-colors duration-200">
                        <Link href={`/episodes/${episode.slug}`}>{episode.title}</Link>
                      </h3>

                      {/* Guest */}
                      <p className="text-red-600 font-semibold text-sm">with {episode.guest}</p>

                      {/* Description */}
                      <p className="text-slate-600 text-sm line-clamp-2 leading-relaxed">{episode.description}</p>

                      {/* Metadata */}
                      <div className="flex items-center justify-between pt-2 border-t border-slate-100">
                        <div className="flex items-center gap-3 text-slate-500 text-xs">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{episode.date}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span>{episode.views} views</span>
                          </div>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 p-0 h-auto font-medium"
                          asChild
                        >
                          <Link href={`/episodes/${episode.slug}`}>Watch →</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            /* No Results */
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-2xl font-bold text-slate-800 mb-2">No episodes found</h3>
                <p className="text-slate-600 mb-6">
                  We couldn't find any episodes matching "{searchQuery}". Try searching with different keywords.
                </p>
                <Button onClick={() => setSearchQuery("")} className="bg-red-600 hover:bg-red-700 text-white">
                  View All Episodes
                </Button>
              </div>
            </div>
          )}

          {/* Load More Hint */}
          {!searchQuery && filteredEpisodes.length > 0 && (
            <div className="text-center mt-16 pt-8 border-t border-slate-200">
              <p className="text-slate-600 mb-4">Want to see more episodes? Check back every Friday for new content!</p>
              <Button
                variant="outline"
                className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white bg-transparent"
              >
                Subscribe for Updates
              </Button>
            </div>
          )}
        </div>
      </section>
    </>
  )
}
