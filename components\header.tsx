import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface HeaderProps {
   currentPage?: string;
}

export default function Header({ currentPage = "Home" }: HeaderProps) {
   const navItems = [
      { name: "Home", href: "/" },
      { name: "Episodes", href: "/episodes" },
      { name: "About", href: "/about" },
      { name: "Blog", href: "/blog" },
   ];

   return (
      // <header className="bg-yellow-400 px-4 py-4 lg:px-8 bg-gradient-to-br from-yellow-200 to-yellow-400">
      // <header className="fixed w-full h-[100px] z-50 top-0 px-4 py-4 lg:px-8">
      <header className="bg-white shadow-lg border-b-4 border-yellow-400 sticky top-0 z-50 py-4">
         <div className="mx-auto max-w-7xl">
            <div className="flex items-center justify-between">
               {/* Logo */}
               <Link href="/" className="flex items-center">
                  <Image
                     src="/images/logo.png"
                     alt="StarBite Logo"
                     width={100}
                     height={100}
                     className="h-16 w-auto mt-1"
                  />
               </Link>

               {/* Navigation */}
               <nav className="hidden md:flex items-center space-x-2 lg:space-x-4">
                  {navItems.map((item) => (
                     <Button
                        key={item.name}
                        asChild
                        // className={
                        //    item.name === currentPage
                        //       ? "bg-red-600 hover:bg-red-700 text-white px-6 !py-2 h-auto rounded-full font-medium border-2 border-red-600"
                        //       : "border-yellow-600 border-2 text-yellow-700 hover:bg-red-600 hover:text-white hover:border-red-600 rounded-full px-6 !py-2 h-auto"
                        // }
                        className={
                           item.name === currentPage
                              ? "bg-yellow-600 hover:bg-yellow-600 text-white px-6 !py-2 h-auto rounded-full font-medium border-2 border-yellow-600"
                              : "border-yellow-600 border-2 text-yellow-700 hover:bg-yellow-600 hover:text-white hover:border-yellow-600 rounded-full px-6 !py-2 h-auto"
                        }
                        variant={
                           item.name === currentPage ? "default" : "outline"
                        }
                     >
                        <Link href={item.href}>{item.name}</Link>
                     </Button>
                  ))}
                  <div className="relative">
                     <Input
                        placeholder="Search"
                        className="border-2 border-yellow-600 placeholder:text-yellow-700 text-black px-4 py-2 pr-10 rounded-full w-32 lg:w-44"
                     />
                     <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-yellow-700" />
                  </div>
               </nav>

               {/* Mobile menu button */}
               <Button
                  variant="outline"
                  size="sm"
                  className="md:hidden border-2 border-yellow-600 bg-yellow-400 hover:bg-yellow-500"
               >
                  Menu
               </Button>
            </div>
         </div>
      </header>
   );
}
