import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function HeroSection() {
   return (
      <section className="px-4 sm:px-8 pt-12 lg:pt-24 flex items-center relative overflow-hidden">
         <Image
            src="/images/hero-bg-red.png"
            alt="Hero Image"
            fill
            className="object-cover z-10 absolute inset-0"
         />

         <div className="mx-auto max-w-7xl w-full z-20">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
               {/* Left Content */}
               <div className="space-y-6 py-12 lg:py-24">
                  <div className="space-y-2">
                     <h1 className="text-7xl font-black font-poppins text-white tracking-wide uppercase">
                        StaR Bite
                     </h1>
                     <h3 className="text-3xl font-black font-poppins text-white tracking-wide uppercase">
                        with <PERSON><PERSON>
                     </h3>
                  </div>

                  <p className="text-xl leading-8 max-w-3xl text-gray-100">
                     Nigeria's boldest and most entertaining food review show.
                     Discover where to eat, what to avoid, and join celebrity
                     guests for unfiltered reactions.
                  </p>

                  <div className="pt-6">
                     <Button
                        size="lg"
                        className="bg-red-800 border-2 border-yellow-500 hover:bg-red-800/80 text-white px-8 py-6 text-lg font-bold rounded-full"
                        asChild
                     >
                        <Link href="/episodes">WATCH LATEST EPISODE</Link>
                        {/* <Link href="/episodes">Watch Episodes</Link> */}
                     </Button>
                  </div>
               </div>

               {/* Right Content - Video Placeholder */}
               <div className="w-full flex justify-center">
                  <div className="relative  group h-[500px] aspect-square flex items-center justify-center">
                     <Image
                        src="/images/download.png"
                        alt="Katarina Ataman photo"
                        fill
                        // className="object-contain scale-110  transition-transform duration-300 ease-in-out group-hover:scale-125 bg-top brightness-150"
                        className="object-contain scale-110  transition-transform duration-300 ease-in-out bg-top brightness-150"
                     />
                  </div>
               </div>
            </div>
         </div>
      </section>
   );
}
