import { <PERSON><PERSON> } from "@/components/ui/button";
import { Play } from "lucide-react";
import PlayButton from "./play-button";

export default function AboutSection() {
   return (
      <section className="bg-yellow-400 px-4 sm:px-8 py-12 lg:py-20">
         <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
               {/* Left Content */}
               <div className="space-y-6">
                  <div className="space-y-4">
                     <h2 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-slate-800 leading-tight">
                        TAKE A SEAT AT THE TABLE
                     </h2>
                     <p className="text-lg lg:text-xl text-slate-800">
                        Bio and discussions here
                     </p>
                  </div>

                  <Button
                     size="lg"
                     className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 text-lg font-bold rounded-3xl"
                  >
                     READ ABOUT THE SHOW
                  </Button>
               </div>

               {/* Right Content - Video Placeholder */}
               <div className="relative">
                  <div className="aspect-video shadow-xl bg-gray-300 rounded-3xl flex items-center justify-center">
                     <PlayButton
                        videoId="fimJNnaYbJY"
                        title="StarBite Trailer"
                     />
                  </div>
               </div>
            </div>
         </div>
      </section>
   );
}
