"use client";

import { But<PERSON> } from "@/components/ui/button";
import VideoModal from "@/components/video-modal";
import { Play } from "lucide-react";
import { useState } from "react";

interface PlayButtonProps {
   videoId: string;
   title?: string;
   size?: "sm" | "md" | "lg";
   className?: string;
   variant?: "primary" | "secondary" | "outline";
}

export default function PlayButton({
   videoId,
   title,
   size = "md",
   className = "",
   variant = "primary",
}: PlayButtonProps) {
   const [isModalOpen, setIsModalOpen] = useState(false);

   const getSizeClasses = () => {
      switch (size) {
         case "sm":
            return "p-2";
         case "lg":
            return "p-6";
         case "md":
         default:
            return "p-4";
      }
   };

   const getVariantClasses = () => {
      switch (variant) {
         case "secondary":
            return "bg-gray-500 hover:bg-gray-600 text-white";
         case "outline":
            return "bg-transparent border-2 border-white text-white hover:bg-white/20";
         case "primary":
         default:
            return "bg-red-600 hover:bg-red-700 text-white";
      }
   };

   return (
      <>
         <Button
            size="lg"
            className={`rounded-full ${className} !p-4 bg-yellow-200/80 hover:bg-yellow-200/90 hover:text-red-600 flex justify-center h-auto z-10`}
            onClick={() => setIsModalOpen(true)}
         >
            <svg
               width="25"
               height="24"
               viewBox="0 0 25 24"
               xmlns="http://www.w3.org/2000/svg"
               className={`${
                  size === "sm"
                     ? "!h-6 !w-6"
                     : size === "lg"
                     ? "!h-14 !w-14"
                     : "!h-8 !w-8"
               } fill-yellow-800`}
            >
               <path d="M19.4357 13.9174C20.8659 13.0392 20.8659 10.9608 19.4357 10.0826L9.55234 4.01389C8.05317 3.09335 6.125 4.17205 6.125 5.93128L6.125 18.0688C6.125 19.828 8.05317 20.9067 9.55234 19.9861L19.4357 13.9174Z" />
            </svg>
            <span className="sr-only">Play {title || "video"}</span>
         </Button>

         <VideoModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            videoId={videoId}
            title={title}
         />
      </>
   );
}
