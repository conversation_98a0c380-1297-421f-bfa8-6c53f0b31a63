import type { Metadata } from "next"
import Link from "next/link"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { CalendarIcon, Clock, Tag, Share2, Facebook, Twitter, Instagram } from "lucide-react"

export const metadata: Metadata = {
  title: "Blog Post | StarBite with Katarina",
  description: "Read the latest articles, recipes, and behind-the-scenes content from StarBite with Katarina.",
}

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  // Mock blog post data
  const post = {
    title: "The Evolution of Nigerian Street Food",
    excerpt:
      "Discover the rich flavors and cultural significance behind Nigeria's most beloved street food dishes and how they've evolved over time.",
    date: "July 15, 2025",
    readTime: "8 min read",
    category: "Food",
    author: "Katarina",
    content: `
      <p class="text-lg mb-4">Nigerian street food is a vibrant tapestry of flavors, aromas, and cultural influences that has evolved over generations. From the bustling markets of Lagos to the street corners of Abuja, these culinary treasures tell the story of Nigeria's rich heritage and diverse communities.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">The Origins of Nigerian Street Food</h2>
      
      <p class="mb-4">The tradition of street food in Nigeria dates back centuries, with vendors selling quick, affordable meals to travelers, workers, and passersby. These early offerings were simple but satisfying, often featuring ingredients that were locally available and could be prepared with minimal equipment.</p>
      
      <p class="mb-4">As urbanization increased in the 20th century, street food culture flourished in Nigeria's growing cities. Markets and transportation hubs became centers for food vendors, each specializing in their own regional specialties.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">Popular Nigerian Street Foods</h2>
      
      <h3 class="text-xl font-bold mt-6 mb-3">Suya</h3>
      
      <p class="mb-4">Perhaps Nigeria's most famous street food, suya consists of skewered meat (typically beef) that is seasoned with a complex spice mixture called yaji. This spice blend typically includes ground peanuts, chili pepper, ginger, and other aromatic spices. The meat is grilled over open flames, creating a smoky, spicy delicacy that's irresistible.</p>
      
      <h3 class="text-xl font-bold mt-6 mb-3">Akara</h3>
      
      <p class="mb-4">These bean fritters are made from black-eyed peas that are soaked, peeled, and ground into a paste. The paste is seasoned with onions and spices, then deep-fried in small portions. The result is a crispy exterior with a soft, flavorful interior. Akara is often eaten for breakfast or as a snack, paired with bread or ogi (fermented corn porridge).</p>
      
      <h3 class="text-xl font-bold mt-6 mb-3">Puff Puff</h3>
      
      <p class="mb-4">These sweet, deep-fried dough balls are Nigeria's answer to doughnuts. Made from a simple batter of flour, yeast, sugar, and water, puff puff is fried until golden and puffy. They're often served hot and can be dusted with sugar or drizzled with honey.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">Modern Innovations</h2>
      
      <p class="mb-4">Today's Nigerian street food scene is a blend of tradition and innovation. Young chefs are reimagining classic recipes with modern techniques and presentation styles, while still honoring the authentic flavors that make these dishes beloved.</p>
      
      <p class="mb-4">Food trucks and pop-up restaurants have also entered the scene, bringing street food favorites to new audiences and elevating their status. Social media has played a significant role in this evolution, with food influencers showcasing Nigeria's street food culture to global audiences.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">The Future of Nigerian Street Food</h2>
      
      <p class="mb-4">As Nigerian cuisine gains international recognition, its street food traditions are poised to reach new heights. Chefs around the world are incorporating Nigerian flavors and techniques into their menus, while diaspora communities continue to share these culinary treasures with their adopted countries.</p>
      
      <p class="mb-4">Despite these changes, the heart of Nigerian street food remains the same: delicious, accessible food that brings people together and celebrates the country's rich cultural heritage.</p>
    `,
    relatedPosts: [
      {
        id: 2,
        title: "5 Essential Nigerian Spices Every Kitchen Needs",
        excerpt: "Discover the key spices that give Nigerian cuisine its distinctive flavors.",
        date: "July 10, 2025",
        category: "Recipes",
      },
      {
        id: 3,
        title: "Behind the Scenes: Filming in Lagos Markets",
        excerpt: "Join us as we explore the vibrant food markets of Lagos.",
        date: "July 5, 2025",
        category: "Culture",
      },
      {
        id: 4,
        title: "Celebrity Chef Spotlight: Interview with Chef Tunde",
        excerpt: "Learn about Chef Tunde's journey and his favorite Nigerian dishes.",
        date: "June 28, 2025",
        category: "Interviews",
      },
    ],
  }

  return (
    <main className="min-h-screen">
      <Header currentPage="Blog" />

      {/* Hero Banner */}
      <section className="bg-yellow-400 px-4 py-12">
        <div className="mx-auto max-w-4xl">
          <div className="text-center">
            <Badge className="bg-red-600 hover:bg-red-700 text-white mb-4">{post.category}</Badge>
            <h1 className="text-3xl lg:text-5xl font-bold text-slate-800 mb-4">{post.title}</h1>
            <p className="text-xl text-slate-700 max-w-2xl mx-auto mb-6">{post.excerpt}</p>

            <div className="flex items-center justify-center gap-6 text-slate-700">
              <div className="flex items-center gap-1">
                <CalendarIcon className="h-4 w-4" />
                <span className="text-sm">{post.date}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span className="text-sm">{post.readTime}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-sm">By {post.author}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <div className="bg-white">
        <div className="mx-auto max-w-4xl px-4">
          <div className="aspect-[21/9] bg-gray-300 -mt-6 rounded-lg overflow-hidden">
            {/* Placeholder for blog post featured image */}
            <div className="w-full h-full bg-gray-300 flex items-center justify-center">
              <span className="text-gray-500 text-lg">Featured Image</span>
            </div>
          </div>
        </div>
      </div>

      {/* Blog Content */}
      <section className="bg-white px-4 py-12">
        <div className="mx-auto max-w-4xl">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-8">
              <article className="prose prose-lg max-w-none">
                <div dangerouslySetInnerHTML={{ __html: post.content }} />
              </article>

              {/* Tags */}
              <div className="mt-8 pt-8 border-t border-slate-200">
                <div className="flex flex-wrap gap-2">
                  <span className="text-slate-700 font-medium flex items-center">
                    <Tag className="h-4 w-4 mr-2" />
                    Tags:
                  </span>
                  {["Nigerian Cuisine", "Street Food", "Food Culture", "Lagos"].map((tag) => (
                    <Badge key={tag} className="bg-slate-100 text-slate-700 hover:bg-slate-200">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Share */}
              <div className="mt-8 pt-8 border-t border-slate-200">
                <div className="flex items-center gap-4">
                  <span className="text-slate-700 font-medium flex items-center">
                    <Share2 className="h-4 w-4 mr-2" />
                    Share:
                  </span>
                  <Button variant="outline" size="icon" className="rounded-full h-8 w-8 bg-transparent">
                    <Facebook className="h-4 w-4" />
                    <span className="sr-only">Share on Facebook</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full h-8 w-8 bg-transparent">
                    <Twitter className="h-4 w-4" />
                    <span className="sr-only">Share on Twitter</span>
                  </Button>
                  <Button variant="outline" size="icon" className="rounded-full h-8 w-8 bg-transparent">
                    <Instagram className="h-4 w-4" />
                    <span className="sr-only">Share on Instagram</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-4">
              {/* Author */}
              <Card className="mb-6">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center">
                    <div className="w-20 h-20 rounded-full bg-gray-300 mb-4">{/* Author image placeholder */}</div>
                    <h3 className="font-bold text-lg text-slate-800 mb-2">{post.author}</h3>
                    <p className="text-slate-600 text-sm mb-4">
                      Host of StarBite with Katarina and passionate food enthusiast with a deep love for Nigerian
                      cuisine.
                    </p>
                    <Button variant="outline" className="w-full bg-transparent">
                      View Profile
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Related Posts */}
              <div className="space-y-4">
                <h3 className="font-bold text-lg text-slate-800">Related Posts</h3>
                {post.relatedPosts.map((relatedPost) => (
                  <Card key={relatedPost.id} className="overflow-hidden">
                    <div className="flex">
                      <div className="w-1/3 bg-gray-300">{/* Thumbnail placeholder */}</div>
                      <div className="w-2/3 p-3">
                        <Badge className="bg-yellow-400 text-slate-800 text-xs mb-1">{relatedPost.category}</Badge>
                        <h4 className="font-bold text-sm text-slate-800 line-clamp-2 mb-1">
                          <Link href={`/blog/${relatedPost.id}`} className="hover:text-red-600">
                            {relatedPost.title}
                          </Link>
                        </h4>
                        <div className="text-xs text-slate-500">{relatedPost.date}</div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Newsletter */}
              <Card className="mt-6 bg-red-600 text-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-2">Subscribe to Our Newsletter</h3>
                  <p className="text-white/80 text-sm mb-4">
                    Get the latest recipes and articles delivered to your inbox.
                  </p>
                  <input
                    type="email"
                    placeholder="Your email"
                    className="w-full px-3 py-2 rounded-md text-slate-800 mb-2"
                  />
                  <Button className="w-full bg-yellow-400 hover:bg-yellow-500 text-slate-800">Subscribe</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
