"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Search, X } from "lucide-react"
import { Button } from "@/components/ui/button"

interface EpisodeSearchProps {
  onSearch?: (query: string) => void
}

export default function EpisodeSearch({ onSearch }: EpisodeSearchProps) {
  const [searchQuery, setSearchQuery] = useState("")

  const handleSearch = (value: string) => {
    setSearchQuery(value)
    onSearch?.(value)
  }

  const clearSearch = () => {
    setSearchQuery("")
    onSearch?.("")
  }

  return (
    <section className="bg-yellow-400 px-4 py-12">
      <div className="mx-auto max-w-4xl">
        <div className="text-center mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold text-slate-800 mb-2">Find Your Favorite Episodes</h2>
          <p className="text-slate-700">
            Search through our collection of culinary adventures and celebrity conversations
          </p>
        </div>

        <div className="relative max-w-2xl mx-auto">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-500" />
            <Input
              type="text"
              placeholder="Search episodes by title, guest, or keywords..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-12 pr-12 py-4 text-lg border-2 border-slate-300 focus:border-red-500 rounded-full bg-white shadow-lg"
            />
            {searchQuery && (
              <Button
                onClick={clearSearch}
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-slate-100 rounded-full"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </Button>
            )}
          </div>

          {searchQuery && (
            <div className="mt-4 text-center">
              <p className="text-slate-700">
                Searching for: <span className="font-semibold">"{searchQuery}"</span>
              </p>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
