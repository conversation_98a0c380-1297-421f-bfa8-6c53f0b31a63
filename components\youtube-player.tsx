"use client"

import { useState } from "react"
import { Loader2 } from "lucide-react"

interface YouTubePlayerProps {
  videoId: string
  title?: string
  className?: string
}

export default function YouTubePlayer({ videoId, title, className = "" }: YouTubePlayerProps) {
  const [isLoading, setIsLoading] = useState(true)

  const handleIframeLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className={`relative w-full aspect-video bg-black rounded-lg overflow-hidden ${className}`}>
      {/* Loading Spinner */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-10">
          <Loader2 className="h-12 w-12 text-red-600 animate-spin" />
        </div>
      )}

      {/* YouTube Embed */}
      <iframe
        className="w-full h-full"
        src={`https://www.youtube.com/embed/${videoId}?rel=0`}
        title={title || "YouTube video player"}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        onLoad={handleIframeLoad}
      ></iframe>
    </div>
  )
}
