import EpisodeList from "@/components/episode-list";
import EpisodeSearch from "@/components/episode-search";
import Footer from "@/components/footer";
import Header from "@/components/header";
import LatestEpisode from "@/components/latest-episode";
import { getAllEpisodes, getLatestEpisode } from "@/lib/data";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: "Episodes | StarBite with Katarina - Nigerian Cuisine Food Show",
   description:
      "Watch all episodes of StarBite with Katarina featuring Nigerian cuisine, celebrity guests, and culinary adventures. Search and discover your favorite episodes.",
   keywords:
      "Nigerian cuisine, food review, episodes, celebrity talk show, Katarina, PimPim, cooking show, street food",
   openGraph: {
      title: "Episodes | StarBite with Katarina",
      description:
         "Watch all episodes featuring Nigerian cuisine and celebrity guests",
      type: "website",
   },
};

export default async function EpisodesPage() {
   // Fetch data on the server
   const [latestEpisode, allEpisodes] = await Promise.all([
      getLatestEpisode(),
      getAllEpisodes(),
   ]);

   return (
      <main className="min-h-screen">
         <Header currentPage="Episodes" />
         <LatestEpisode episode={latestEpisode} />
         <EpisodeList episodes={allEpisodes} />
         <Footer />
      </main>
   );
}
