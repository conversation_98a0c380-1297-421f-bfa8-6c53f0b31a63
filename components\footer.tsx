import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function Footer() {
   return (
      <footer className="bg-red-600 px-4 sm:px-8 py-8 lg:py-12">
         <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
               {/* Left - Newsletter Signup */}
               <div className="space-y-4">
                  <h3 className="text-white font-bold text-lg lg:text-xl">
                     SUBSCRIBE FOR UPDATES
                  </h3>
                  <div className="flex gap-2 max-w-md">
                     <Input
                        type="email"
                        placeholder="Enter your email"
                        className="bg-white border-white text-black placeholder:text-gray-500 flex-1"
                     />
                     <Button className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-6 py-2 rounded-md">
                        SUBSCRIBE
                     </Button>
                  </div>
               </div>

               {/* Right - Social Media */}
               <div className="space-y-4 lg:text-right">
                  <h3 className="text-white font-bold text-lg lg:text-xl">
                     SOCIALS
                  </h3>
                  <div className="flex gap-2 lg:justify-end">
                     {/* Social Media Icons - Using yellow squares as placeholders */}
                     <div className="w-8 h-8 bg-yellow-400 rounded"></div>
                     <div className="w-8 h-8 bg-yellow-400 rounded"></div>
                     <div className="w-8 h-8 bg-yellow-400 rounded"></div>
                     <div className="w-8 h-8 bg-yellow-400 rounded"></div>
                  </div>
               </div>
            </div>

            {/* Copyright */}
            <div className="mt-8 pt-8 border-t border-red-500 text-center">
               <p className="text-white text-sm">
                  Copyright ©2006-2025 StarBite with Katarina
               </p>
            </div>
         </div>
      </footer>
   );
}
