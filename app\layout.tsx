import type { <PERSON>ada<PERSON> } from "next";
import { Cherry_Bomb_One, <PERSON><PERSON><PERSON>, <PERSON>, Pop<PERSON><PERSON> } from "next/font/google";
import type React from "react";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

const poppins = Poppins({
   subsets: ["latin"],
   weight: ["400", "500", "600", "700"],
   variable: "--font-poppins",
});

const coiny = Coiny({
   subsets: ["latin"],
   weight: "400",
   variable: "--font-coiny",
});

const cherrybomb = Cherry_Bomb_One({
   subsets: ["latin"],
   weight: "400",
   variable: "--font-cherrybomb",
});

export const metadata: Metadata = {
   title: "StarBite with Katarina",
   description:
      "A food review & celebrity talk show showcasing Nigerian cuisine",
};

export default function RootLayout({
   children,
}: {
   children: React.ReactNode;
}) {
   return (
      <html lang="en">
         <body
            className={`${inter.className} ${coiny.variable} ${cherrybomb.variable} ${poppins.variable}`}
         >
            {children}
         </body>
      </html>
   );
}
