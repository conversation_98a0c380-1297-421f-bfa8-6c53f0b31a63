"use client";

import PlayButton from "@/components/play-button";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useMobile } from "@/hooks/use-mobile";
import type { UpcomingGuest } from "@/lib/data";
import { Plus } from "lucide-react";
import Link from "next/link";

interface UpcomingGuestsSectionProps {
   guests: UpcomingGuest[];
}

// Placeholder guest data
const createPlaceholderGuest = (index: number): UpcomingGuest => ({
   id: -index,
   name: "Mystery Guest",
   description: "Exciting new guest announcement coming soon",
   duration: "TBD",
   date: "TBA",
   category: "Upcoming",
});

export default function UpcomingGuestsSection({
   guests,
}: UpcomingGuestsSectionProps) {
   const isMobile = useMobile();

   // Create display guests with placeholders if needed (only on desktop)
   const displayGuests = () => {
      const targetCount = 4;
      const actualGuests = guests.slice(0, targetCount);

      if (isMobile || actualGuests.length >= targetCount) {
         return actualGuests;
      }

      // Add placeholders for desktop
      const placeholdersNeeded = targetCount - actualGuests.length;
      const placeholders = Array.from({ length: placeholdersNeeded }, (_, i) =>
         createPlaceholderGuest(i + 1)
      );

      return [...actualGuests, ...placeholders];
   };

   return (
      <section className="bg-slate-800 px-4 sm:px-8 py-12 lg:py-20">
         <div className="mx-auto max-w-7xl">
            {/* Header */}
            <div className="flex items-center justify-between mb-8 lg:mb-12">
               <h2 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-white">
                  UPCOMING GUESTS
               </h2>
               <Button
                  variant="outline"
                  className="border-2 border-yellow-400 bg-transparent hover:bg-yellow-400 text-yellow-400 hover:text-black px-6 py-2 rounded-md font-bold"
                  asChild
               >
                  <Link href="/upcoming-guests">VIEW ALL</Link>
               </Button>
            </div>

            {/* Guests Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
               {displayGuests().map((guest) => (
                  <Card key={guest.id} className="bg-transparent border-none">
                     <div className="space-y-4">
                        {/* Video Placeholder */}
                        <div className="aspect-video bg-gray-300 rounded-lg flex items-center justify-center relative">
                           {guest.id < 0 ? (
                              // Placeholder guest
                              <div className="flex flex-col items-center justify-center text-gray-500">
                                 <Plus className="h-8 w-8 mb-2" />
                                 <span className="text-sm font-medium">
                                    Coming Soon
                                 </span>
                              </div>
                           ) : guest.videoId ? (
                              // Guest with trailer video
                              <PlayButton
                                 videoId={guest.videoId}
                                 title={`${guest.name} Preview`}
                                 variant="secondary"
                              />
                           ) : (
                              // Guest without video
                              <div className="flex flex-col items-center justify-center text-gray-500">
                                 <span className="text-sm font-medium">
                                    Preview Coming Soon
                                 </span>
                              </div>
                           )}
                        </div>

                        {/* Guest Info */}
                        <div className="space-y-2">
                           <h3
                              className={`font-bold text-lg ${
                                 guest.id < 0 ? "text-white/60" : "text-white"
                              }`}
                           >
                              {guest.name}
                           </h3>
                           <p
                              className={`text-sm line-clamp-2 ${
                                 guest.id < 0 ? "text-white/60" : "text-white"
                              }`}
                           >
                              {guest.description}
                           </p>
                           <p
                              className={`text-sm ${
                                 guest.id < 0 ? "text-white/60" : "text-white"
                              }`}
                           >
                              {guest.duration}
                           </p>
                        </div>
                     </div>
                  </Card>
               ))}
            </div>
         </div>
      </section>
   );
}
