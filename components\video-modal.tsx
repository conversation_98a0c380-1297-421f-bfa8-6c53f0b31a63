"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";

interface VideoModalProps {
   isOpen: boolean;
   onClose: () => void;
   videoId: string;
   title?: string;
}

export default function VideoModal({
   isOpen,
   onClose,
   videoId,
   title,
}: VideoModalProps) {
   const [isLoading, setIsLoading] = useState(true);

   // Reset loading state when video changes
   useEffect(() => {
      if (isOpen) {
         setIsLoading(true);
      }
   }, [videoId, isOpen]);

   const handleIframeLoad = () => {
      setIsLoading(false);
   };

   return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
         <DialogContent className="sm:max-w-[900px] p-0 bg-transparent border-none">
            <DialogTitle className="sr-only">Watch Video</DialogTitle>
            <div className="relative w-full aspect-video bg-black rounded-3xl overflow-hidden">
               {/* Loading Spinner */}
               {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-10">
                     <Loader2 className="h-12 w-12 text-red-600 animate-spin" />
                  </div>
               )}

               {/* YouTube Embed */}
               <iframe
                  className="w-full h-full"
                  src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`}
                  title={title || "YouTube video player"}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  onLoad={handleIframeLoad}
               ></iframe>
            </div>
         </DialogContent>
      </Dialog>
   );
}
