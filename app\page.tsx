import AboutSection from "@/components/about-section";
import EpisodesSection from "@/components/episodes-section";
import Footer from "@/components/footer";
import Header from "@/components/header";
import HeroSection from "@/components/hero-section";
// import UpcomingGuestsSection from "@/components/upcoming-guests-section";
import { getAllUpcomingGuests, getPopularEpisodes } from "@/lib/data";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: "StarBite with Katarina - Nigerian Cuisine Food Review & Celebrity Talk Show",
   description:
      "Join <PERSON> for thrilling food reviews and celebrity conversations showcasing the vibrant world of Nigerian cuisine on PimPim.",
   keywords:
      "Nigerian cuisine, food review, celebrity talk show, Katarina, PimPim, cooking show",
   openGraph: {
      title: "StarBite with Katarina",
      description:
         "A food review & celebrity talk show showcasing Nigerian cuisine",
      type: "website",
   },
};

export default async function HomePage() {
   // Fetch data on the server
   const [popularEpisodes, upcomingGuests] = await Promise.all([
      getPopularEpisodes(),
      getAllUpcomingGuests(),
   ]);

   return (
      <main className="min-h-screen">
         <Header />
         <HeroSection />
         <EpisodesSection episodes={popularEpisodes} />
         {/* <UpcomingGuestsSection guests={upcomingGuests} /> */}
         <AboutSection />
         <Footer />
      </main>
   );
}
