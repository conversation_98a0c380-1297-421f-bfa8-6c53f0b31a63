// Types
export interface Episode {
   id: number;
   title: string;
   guest: string;
   description: string;
   date: string;
   duration: string;
   category: string;
   isPopular?: boolean;
   isLatest?: boolean;
   isNew?: boolean;
   thumbnail?: string;
   slug: string;
   videoId: string; // Added videoId field
}

export interface BlogPost {
   id: number;
   title: string;
   excerpt: string;
   content?: string;
   date: string;
   readTime: string;
   category: string;
   featured?: boolean;
   author: string;
   slug: string;
   tags?: string[];
}

export interface Guest {
   id: number;
   name: string;
   role: string;
   bio: string;
   image?: string;
   episodeId: number;
}

export interface UpcomingGuest {
   id: number;
   name: string;
   description: string;
   duration: string;
   date: string;
   category: string;
   videoId?: string; // Added optional videoId field
}

// Episodes Data
export const episodesData: Episode[] = [
   {
      id: 1,
      title: "Street Food Adventures in Abuja",
      guest: "Chef <PERSON><PERSON>",
      description:
         "Join <PERSON> as she explores the vibrant street food scene in Nigeria's capital city, discovering hidden gems and meeting the passionate vendors who keep these culinary traditions alive.",
      date: "July 20, 2025",
      duration: "28:45",
      category: "Street Food",
      isPopular: true,
      isLatest: true,
      isNew: true,
      slug: "street-food-adventures-abuja",
      videoId: "dQw4w9WgXcQ", // Example YouTube video ID
   },
   {
      id: 2,
      title: "The Art of Perfect Jollof Rice",
      guest: "Grandma Adeola",
      description:
         "Learning traditional Jollof rice secrets from a master cook who has perfected the recipe over decades.",
      date: "July 15, 2025",
      duration: "32:18",
      category: "Traditional",
      isPopular: true,
      slug: "art-of-perfect-jollof-rice",
      videoId: "9bZkp7q19f0", // Example YouTube video ID
   },
   {
      id: 3,
      title: "Modern Nigerian Fusion with Chef David",
      guest: "Chef David Okonkwo",
      description:
         "Innovative takes on classic Nigerian dishes that bridge traditional flavors with contemporary techniques.",
      date: "July 10, 2025",
      duration: "26:33",
      category: "Fusion",
      slug: "modern-nigerian-fusion-chef-david",
      videoId: "jNQXAC9IVRw", // Example YouTube video ID
   },
];

// Blog Posts Data
export const blogPostsData: BlogPost[] = [
   {
      id: 1,
      title: "The Evolution of Nigerian Street Food",
      excerpt:
         "Discover the rich flavors and cultural significance behind Nigeria's most beloved street food dishes and how they've evolved over time.",
      date: "July 15, 2025",
      readTime: "8 min read",
      category: "Food",
      featured: true,
      author: "Katarina",
      slug: "evolution-nigerian-street-food",
      tags: ["Nigerian Cuisine", "Street Food", "Food Culture", "Lagos"],
      content: `
      <p class="text-lg mb-4">Nigerian street food is a vibrant tapestry of flavors, aromas, and cultural influences that has evolved over generations. From the bustling markets of Lagos to the street corners of Abuja, these culinary treasures tell the story of Nigeria's rich heritage and diverse communities.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">The Origins of Nigerian Street Food</h2>
      
      <p class="mb-4">The tradition of street food in Nigeria dates back centuries, with vendors selling quick, affordable meals to travelers, workers, and passersby. These early offerings were simple but satisfying, often featuring ingredients that were locally available and could be prepared with minimal equipment.</p>
      
      <p class="mb-4">As urbanization increased in the 20th century, street food culture flourished in Nigeria's growing cities. Markets and transportation hubs became centers for food vendors, each specializing in their own regional specialties.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">Popular Nigerian Street Foods</h2>
      
      <h3 class="text-xl font-bold mt-6 mb-3">Suya</h3>
      
      <p class="mb-4">Perhaps Nigeria's most famous street food, suya consists of skewered meat (typically beef) that is seasoned with a complex spice mixture called yaji. This spice blend typically includes ground peanuts, chili pepper, ginger, and other aromatic spices. The meat is grilled over open flames, creating a smoky, spicy delicacy that's irresistible.</p>
      
      <h3 class="text-xl font-bold mt-6 mb-3">Akara</h3>
      
      <p class="mb-4">These bean fritters are made from black-eyed peas that are soaked, peeled, and ground into a paste. The paste is seasoned with onions and spices, then deep-fried in small portions. The result is a crispy exterior with a soft, flavorful interior. Akara is often eaten for breakfast or as a snack, paired with bread or ogi (fermented corn porridge).</p>
      
      <h3 class="text-xl font-bold mt-6 mb-3">Puff Puff</h3>
      
      <p class="mb-4">These sweet, deep-fried dough balls are Nigeria's answer to doughnuts. Made from a simple batter of flour, yeast, sugar, and water, puff puff is fried until golden and puffy. They're often served hot and can be dusted with sugar or drizzled with honey.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">Modern Innovations</h2>
      
      <p class="mb-4">Today's Nigerian street food scene is a blend of tradition and innovation. Young chefs are reimagining classic recipes with modern techniques and presentation styles, while still honoring the authentic flavors that make these dishes beloved.</p>
      
      <p class="mb-4">Food trucks and pop-up restaurants have also entered the scene, bringing street food favorites to new audiences and elevating their status. Social media has played a significant role in this evolution, with food influencers showcasing Nigeria's street food culture to global audiences.</p>
      
      <h2 class="text-2xl font-bold mt-8 mb-4">The Future of Nigerian Street Food</h2>
      
      <p class="mb-4">As Nigerian cuisine gains international recognition, its street food traditions are poised to reach new heights. Chefs around the world are incorporating Nigerian flavors and techniques into their menus, while diaspora communities continue to share these culinary treasures with their adopted countries.</p>
      
      <p class="mb-4">Despite these changes, the heart of Nigerian street food remains the same: delicious, accessible food that brings people together and celebrates the country's rich cultural heritage.</p>
    `,
   },
   {
      id: 2,
      title: "5 Essential Nigerian Spices Every Kitchen Needs",
      excerpt:
         "Discover the key spices that give Nigerian cuisine its distinctive flavors and learn how to use them in your cooking.",
      date: "July 10, 2025",
      readTime: "6 min read",
      category: "Recipes",
      author: "Katarina",
      slug: "essential-nigerian-spices",
      tags: ["Spices", "Cooking Tips", "Nigerian Cuisine", "Ingredients"],
   },
   {
      id: 3,
      title: "Behind the Scenes: Filming in Lagos Markets",
      excerpt:
         "Join us as we explore the vibrant food markets of Lagos and the challenges of filming in these bustling environments.",
      date: "July 5, 2025",
      readTime: "5 min read",
      category: "Culture",
      author: "Katarina",
      slug: "filming-lagos-markets",
      tags: ["Behind the Scenes", "Lagos", "Markets", "Production"],
   },
];

// Upcoming Guests Data
export const upcomingGuestsData: UpcomingGuest[] = [
   {
      id: 1,
      name: "Chef Yemi Osunkoya",
      description:
         "Exploring modern interpretations of traditional Yoruba cuisine with this acclaimed chef and restaurateur",
      duration: "25 mins",
      date: "July 27, 2025",
      category: "Traditional",
      videoId: "M7FIvfx5J10", // Example YouTube video ID for trailer
   },
   {
      id: 2,
      name: "Food Historian Dr. Chioma",
      description:
         "The historical significance of Nigerian food culture and its evolution through the centuries",
      duration: "30 mins",
      date: "August 3, 2025",
      category: "Culture",
      videoId: "M7FIvfx5J10", // Example YouTube video ID for trailer
   },
   {
      id: 3,
      name: "Street Food Vendor Mama Ngozi",
      description:
         "40 years of serving authentic Nigerian street food - stories from the streets of Lagos",
      duration: "22 mins",
      date: "August 10, 2025",
      category: "Street Food",
      videoId: "M7FIvfx5J10", // Example YouTube video ID for trailer
   },
   {
      id: 4,
      name: "Celebrity Chef Kwame",
      description:
         "Fusion techniques with traditional Nigerian ingredients - bridging cultures through food",
      duration: "28 mins",
      date: "August 17, 2025",
      category: "Fusion",
      videoId: "M7FIvfx5J10", // Example YouTube video ID for trailer
   },
   {
      id: 5,
      name: "Spice Merchant Alhaji Ibrahim",
      description:
         "The secret world of Nigerian spices and seasonings from a master spice trader",
      duration: "24 mins",
      date: "August 24, 2025",
      category: "Ingredients",
      videoId: "M7FIvfx5J10", // Example YouTube video ID for trailer
   },
   {
      id: 6,
      name: "Pastry Chef Adunni",
      description:
         "Traditional Nigerian desserts meet modern pastry techniques in this sweet episode",
      duration: "26 mins",
      date: "August 31, 2025",
      category: "Desserts",
      videoId: "M7FIvfx5J10", // Example YouTube video ID for trailer
   },
];

// Guest Details Data
export const guestsData: Guest[] = [
   {
      id: 1,
      name: "Chef Tunde Adebayo",
      role: "Celebrity Chef & Restaurateur",
      bio: "Chef Tunde is the owner of the acclaimed Lagos restaurant 'Taste of Nigeria' and has been featured on multiple international cooking shows. He specializes in modernizing traditional Nigerian dishes while preserving their authentic flavors.",
      episodeId: 5,
   },
   {
      id: 2,
      name: "Chef Amara Okafor",
      role: "Street Food Expert",
      bio: "Chef Amara has spent over a decade documenting and preserving Nigeria's street food culture. She's the author of 'Streets of Flavor' and runs popular food tours in Abuja.",
      episodeId: 1,
   },
   {
      id: 3,
      name: "Grandma Adeola",
      role: "Traditional Cook",
      bio: "With over 50 years of cooking experience, Grandma Adeola is known throughout her community for her perfect Jollof rice. She represents the authentic, time-honored cooking methods passed down through generations.",
      episodeId: 2,
   },
];

// Simulated API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Data fetching functions following Next.js guidelines
export async function getAllEpisodes(): Promise<Episode[]> {
   await delay(100); // Simulate network delay
   return episodesData;
}

export async function getEpisodeById(id: number): Promise<Episode | null> {
   await delay(100);
   return episodesData.find((episode) => episode.id === id) || null;
}

export async function getEpisodeBySlug(slug: string): Promise<Episode | null> {
   await delay(100);
   return episodesData.find((episode) => episode.slug === slug) || null;
}

export async function getLatestEpisode(): Promise<Episode | null> {
   await delay(100);
   return episodesData.find((episode) => episode.isLatest) || episodesData[0];
}

export async function getPopularEpisodes(): Promise<Episode[]> {
   await delay(100);
   return episodesData.filter((episode) => episode.isPopular);
}

export async function searchEpisodes(query: string): Promise<Episode[]> {
   await delay(200); // Slightly longer delay for search
   if (!query.trim()) return episodesData;

   const lowercaseQuery = query.toLowerCase();
   return episodesData.filter(
      (episode) =>
         episode.title.toLowerCase().includes(lowercaseQuery) ||
         episode.guest.toLowerCase().includes(lowercaseQuery) ||
         episode.description.toLowerCase().includes(lowercaseQuery) ||
         episode.category.toLowerCase().includes(lowercaseQuery)
   );
}

export async function getAllBlogPosts(): Promise<BlogPost[]> {
   await delay(100);
   return blogPostsData;
}

export async function getBlogPostById(id: number): Promise<BlogPost | null> {
   await delay(100);
   return blogPostsData.find((post) => post.id === id) || null;
}

export async function getBlogPostBySlug(
   slug: string
): Promise<BlogPost | null> {
   await delay(100);
   return blogPostsData.find((post) => post.slug === slug) || null;
}

export async function getFeaturedBlogPost(): Promise<BlogPost | null> {
   await delay(100);
   return blogPostsData.find((post) => post.featured) || null;
}

export async function getRegularBlogPosts(): Promise<BlogPost[]> {
   await delay(100);
   return blogPostsData.filter((post) => !post.featured);
}

export async function searchBlogPosts(query: string): Promise<BlogPost[]> {
   await delay(200);
   if (!query.trim()) return blogPostsData;

   const lowercaseQuery = query.toLowerCase();
   return blogPostsData.filter(
      (post) =>
         post.title.toLowerCase().includes(lowercaseQuery) ||
         post.excerpt.toLowerCase().includes(lowercaseQuery) ||
         post.category.toLowerCase().includes(lowercaseQuery) ||
         post.tags?.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
   );
}

export async function getAllUpcomingGuests(): Promise<UpcomingGuest[]> {
   await delay(100);
   return upcomingGuestsData;
}

export async function getGuestById(id: number): Promise<Guest | null> {
   await delay(100);
   return guestsData.find((guest) => guest.id === id) || null;
}

export async function getGuestByEpisodeId(
   episodeId: number
): Promise<Guest | null> {
   await delay(100);
   return guestsData.find((guest) => guest.episodeId === episodeId) || null;
}

export async function getRelatedEpisodes(
   currentEpisodeId: number,
   category: string
): Promise<Episode[]> {
   await delay(100);
   return episodesData
      .filter(
         (episode) =>
            episode.id !== currentEpisodeId && episode.category === category
      )
      .slice(0, 3);
}

export async function getRelatedBlogPosts(
   currentPostId: number,
   category: string
): Promise<BlogPost[]> {
   await delay(100);
   return blogPostsData
      .filter((post) => post.id !== currentPostId && post.category === category)
      .slice(0, 3);
}
