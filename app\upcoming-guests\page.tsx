import type { Metadata } from "next"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Star, Bell } from "lucide-react"
import { getAllUpcomingGuests } from "@/lib/data"

export const metadata: Metadata = {
  title: "Upcoming Guests | StarBite with Kat<PERSON>",
  description:
    "Discover the exciting lineup of upcoming guests on StarBite with <PERSON><PERSON>. From celebrity chefs to cultural experts, see who's joining us next.",
  keywords: "upcoming guests, Nigerian cuisine, celebrity chefs, food experts, <PERSON><PERSON>, PimPim, cooking show",
  openGraph: {
    title: "Upcoming Guests | StarBite with <PERSON><PERSON>",
    description: "See who's joining us next on StarBite with <PERSON><PERSON>",
    type: "website",
  },
}

export default async function UpcomingGuestsPage() {
  const upcomingGuests = await getAllUpcomingGuests()

  return (
    <main className="min-h-screen">
      <Header />

      {/* Hero Banner */}
      <section className="bg-gradient-to-br from-slate-800 via-slate-700 to-slate-900 px-4 py-16 lg:py-24 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-40 h-40 bg-yellow-400 rounded-full"></div>
          <div className="absolute top-40 right-32 w-32 h-32 bg-yellow-400 rounded-full"></div>
          <div className="absolute bottom-32 left-1/3 w-24 h-24 bg-yellow-400 rounded-full"></div>
        </div>

        <div className="mx-auto max-w-7xl relative z-10">
          <div className="text-center">
            <Badge className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-4 py-2 text-sm mb-6">
              🎬 COMING SOON
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">UPCOMING GUESTS</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
              Get ready for exciting conversations with celebrity chefs, food experts, and cultural ambassadors who will
              share their passion for Nigerian cuisine
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-full">
                <Bell className="h-4 w-4 mr-2" />
                Get Notified
              </Button>
              <Button
                variant="outline"
                className="border-2 border-white text-white hover:bg-white hover:text-slate-800 bg-transparent px-8 py-3 rounded-full"
              >
                View Schedule
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Upcoming Guests Grid */}
      <section className="bg-white px-4 py-16 lg:py-24">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-4">Meet Our Upcoming Guests</h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Each guest brings their unique perspective and expertise to showcase the rich diversity of Nigerian
              cuisine
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {upcomingGuests.map((guest, index) => (
              <Card
                key={guest.id}
                className="overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 bg-white"
              >
                <div className="relative">
                  {/* Guest Image Placeholder */}
                  <div className="aspect-[4/3] bg-gradient-to-br from-gray-800 to-gray-900 relative overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <Star className="h-12 w-12 mx-auto mb-2 text-yellow-400" />
                        <p className="text-sm font-medium">Guest Photo</p>
                        <p className="text-xs opacity-75">Coming Soon</p>
                      </div>
                    </div>

                    {/* Episode Number Badge */}
                    <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                      Episode {upcomingGuests.length + index + 1}
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4 bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold">
                      {guest.category}
                    </div>
                  </div>

                  {/* Guest Info */}
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-bold text-2xl text-slate-800 mb-2">{guest.name}</h3>
                        <p className="text-slate-600 leading-relaxed">{guest.description}</p>
                      </div>

                      {/* Episode Details */}
                      <div className="flex items-center gap-4 text-slate-500 text-sm">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{guest.date}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{guest.duration}</span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button className="bg-red-600 hover:bg-red-700 text-white flex-1">
                          <Bell className="h-4 w-4 mr-2" />
                          Notify Me
                        </Button>
                        <Button
                          variant="outline"
                          className="border-slate-300 text-slate-700 hover:bg-slate-50 bg-transparent"
                        >
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-yellow-400 px-4 py-16 lg:py-24">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-4">Don't Miss Out!</h2>
          <p className="text-lg text-slate-700 mb-8 max-w-2xl mx-auto">
            Be the first to know when new episodes are released and get exclusive behind-the-scenes content from our
            upcoming guest appearances.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="px-4 py-3 rounded-full border-2 border-slate-300 flex-1 text-slate-800"
            />
            <Button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-full whitespace-nowrap">
              Subscribe Now
            </Button>
          </div>

          <p className="text-sm text-slate-600 mt-4">Join 10,000+ food lovers who never miss an episode</p>
        </div>
      </section>

      {/* Recent Episodes */}
      <section className="bg-slate-800 px-4 py-16 lg:py-24">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">While You Wait</h2>
            <p className="text-lg text-white/80 max-w-2xl mx-auto">
              Catch up on our recent episodes featuring amazing guests and delicious Nigerian cuisine
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i} className="bg-slate-700 border-slate-600">
                <div className="aspect-video bg-gray-600 relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white rounded-full p-3">
                      <Star className="h-6 w-6" />
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-white mb-2">Recent Episode {i + 1}</h3>
                  <p className="text-white/80 text-sm mb-3">
                    Amazing conversation with our previous guest about Nigerian cuisine traditions
                  </p>
                  <Button variant="link" className="text-yellow-400 hover:text-yellow-300 p-0 h-auto">
                    Watch Now →
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <Button
              variant="outline"
              className="border-2 border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black bg-transparent px-8 py-3 rounded-full"
            >
              View All Episodes
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
