import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon, Clock } from "lucide-react"
import { getFeaturedBlogPost, getRegularBlogPosts } from "@/lib/data"

export const metadata: Metadata = {
  title: "Blog | StarBite with Katarina",
  description: "Read the latest articles, recipes, and behind-the-scenes content from StarBite with Katarina.",
  keywords: "Nigerian cuisine, blog, recipes, food articles, Katarina, PimPim, cooking tips",
}

export default async function BlogPage() {
  // Fetch data on the server
  const [featuredPost, regularPosts] = await Promise.all([getFeaturedBlogPost(), getRegularBlogPosts()])

  return (
    <main className="min-h-screen">
      <Header currentPage="Blog" />

      {/* Hero Banner */}
      <section className="bg-red-600 px-4 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4">BLOG</h1>
            <p className="text-xl text-white max-w-2xl mx-auto">
              Recipes, stories, and insights from the world of Nigerian cuisine
            </p>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && (
        <section className="bg-yellow-400 px-4 py-12">
          <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <Badge className="bg-red-600 hover:bg-red-700 text-white">FEATURED</Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-slate-800">{featuredPost.title}</h2>
                <p className="text-slate-700 text-lg">{featuredPost.excerpt}</p>
                <div className="flex items-center gap-4 text-slate-700">
                  <div className="flex items-center gap-1">
                    <CalendarIcon className="h-4 w-4" />
                    <span className="text-sm">{featuredPost.date}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">{featuredPost.readTime}</span>
                  </div>
                </div>
                <Button className="bg-red-600 hover:bg-red-700 text-white" asChild>
                  <Link href={`/blog/${featuredPost.slug}`}>Read Article</Link>
                </Button>
              </div>

              <div className="aspect-video bg-gray-300 rounded-lg overflow-hidden">
                <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-500 text-lg">Featured Post Image</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Blog Posts Grid */}
      <section className="bg-white px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div className="space-y-2">
              <h2 className="text-2xl font-bold text-slate-800">Latest Articles</h2>
              <p className="text-slate-600">Discover our most recent stories and recipes</p>
            </div>

            <div className="flex gap-4">
              <select className="bg-white border border-slate-300 rounded-md px-4 py-2 text-slate-800">
                <option>All Categories</option>
                <option>Food</option>
                <option>Culture</option>
                <option>Recipes</option>
                <option>Interviews</option>
                <option>Travel</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularPosts.map((post) => (
              <Card
                key={post.id}
                className="overflow-hidden border-slate-200 hover:border-red-600/50 transition-colors"
              >
                <div className="aspect-video bg-gray-300">
                  <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Post Image</span>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    <Badge className="bg-yellow-400 hover:bg-yellow-500 text-slate-800">{post.category}</Badge>

                    <h3 className="font-bold text-xl text-slate-800 line-clamp-2">
                      <Link href={`/blog/${post.slug}`} className="hover:text-red-600 transition-colors">
                        {post.title}
                      </Link>
                    </h3>

                    <p className="text-slate-600 line-clamp-3">{post.excerpt}</p>

                    <div className="flex items-center justify-between pt-2">
                      <div className="flex items-center gap-1 text-slate-500">
                        <CalendarIcon className="h-4 w-4" />
                        <span className="text-xs">{post.date}</span>
                      </div>

                      <div className="flex items-center gap-1 text-slate-500">
                        <Clock className="h-4 w-4" />
                        <span className="text-xs">{post.readTime}</span>
                      </div>
                    </div>

                    <Button variant="link" className="text-red-600 hover:text-red-700 p-0 h-auto font-medium" asChild>
                      <Link href={`/blog/${post.slug}`}>Read More →</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-12 text-center">
            <Button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3">Load More Articles</Button>
            <p className="text-slate-600 mt-4 text-sm">
              Showing {regularPosts.length} of {regularPosts.length + 1} articles
            </p>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="bg-slate-800 px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center max-w-2xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">Subscribe to Our Newsletter</h2>
            <p className="text-white/80 mb-6">
              Get the latest recipes, cooking tips, and behind-the-scenes content delivered straight to your inbox.
            </p>

            <div className="flex flex-col sm:flex-row gap-2 max-w-md mx-auto">
              <input type="email" placeholder="Enter your email" className="px-4 py-3 rounded-md flex-1" />
              <Button className="bg-red-600 hover:bg-red-700 text-white px-6">Subscribe</Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
