import type { <PERSON>ada<PERSON> } from "next"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Play, Award, Users, Utensils } from "lucide-react"

export const metadata: Metadata = {
  title: "About | StarBite with Katarina",
  description:
    "Learn about StarBite with Katarina, a thrilling food review and celebrity talk show showcasing Nigerian cuisine.",
  keywords: "Nigerian cuisine, about, Katarina, PimPim, food show, celebrity talk show",
}

export default function AboutPage() {
  return (
    <main className="min-h-screen">
      <Header currentPage="About" />

      {/* Hero Banner */}
      <section className="bg-yellow-400 px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-bold text-slate-800">ABOUT THE SHOW</h1>
              <p className="text-xl text-slate-800">
                StarBite with Katarina is a thrilling food review and celebrity talk show showcasing the vibrant world
                of Nigerian cuisine on PimPim.
              </p>
              <Button
                size="lg"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 text-lg font-bold rounded-md"
              >
                WATCH LATEST EPISODE
              </Button>
            </div>

            <div className="relative">
              <div className="aspect-video bg-gray-300 rounded-lg flex items-center justify-center">
                <Button
                  size="lg"
                  variant="secondary"
                  className="bg-gray-500 hover:bg-gray-600 text-white rounded-full p-4"
                >
                  <Play className="h-8 w-8 fill-current" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Show Concept */}
      <section className="bg-white px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-4">THE CONCEPT</h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              StarBite with Katarina brings together the best of Nigerian cuisine and celebrity culture in a unique
              format that celebrates food, conversation, and entertainment.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-yellow-400 border-none shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <Utensils className="h-12 w-12 text-red-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-800 mb-2">Food Reviews</h3>
                <p className="text-slate-700">
                  Exploring the rich flavors and culinary traditions of Nigerian cuisine from street food to fine
                  dining.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-red-600 border-none shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <Users className="h-12 w-12 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Celebrity Guests</h3>
                <p className="text-white/80">
                  Engaging conversations with celebrities, chefs, and cultural icons about food, life, and everything in
                  between.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-none shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <Award className="h-12 w-12 text-yellow-400" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Cultural Celebration</h3>
                <p className="text-white/80">
                  Highlighting the cultural significance of Nigerian cuisine and its influence on global food trends.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Meet Katarina */}
      <section className="bg-red-600 px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="order-2 lg:order-1">
              <div className="aspect-square bg-gray-300 rounded-lg overflow-hidden">
                {/* Placeholder for host image */}
                <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-500 text-lg">Host Image</span>
                </div>
              </div>
            </div>

            <div className="space-y-6 order-1 lg:order-2">
              <h2 className="text-3xl lg:text-4xl font-bold text-white">MEET KATARINA</h2>
              <p className="text-white/90 text-lg">
                Katarina is a passionate food enthusiast and cultural ambassador with a deep love for Nigerian cuisine.
                With her vibrant personality and culinary expertise, she brings a unique perspective to food reviews and
                celebrity interviews.
              </p>
              <p className="text-white/90 text-lg">
                Having traveled extensively throughout Nigeria, Katarina has developed a profound understanding of
                regional flavors and cooking techniques. Her mission is to showcase the richness of Nigerian food
                culture to a global audience.
              </p>
              <p className="text-white/90 text-lg">
                When she's not hosting StarBite, Katarina can be found experimenting with recipes, visiting local
                markets, or collaborating with chefs to create innovative Nigerian-inspired dishes.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Behind the Scenes */}
      <section className="bg-yellow-400 px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-4">BEHIND THE SCENES</h2>
            <p className="text-lg text-slate-700 max-w-3xl mx-auto">
              Take a peek behind the curtain and see how StarBite with Katarina comes to life.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="aspect-square bg-gray-300 rounded-lg overflow-hidden">
                {/* Placeholder for behind the scenes images */}
                <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-500 text-lg">BTS Image {i + 1}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 text-center">
            <Button className="bg-red-600 hover:bg-red-700 text-white">View Gallery</Button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-white px-4 py-12 lg:py-20">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-800 mb-4">FREQUENTLY ASKED QUESTIONS</h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Everything you need to know about StarBite with Katarina.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                question: "When does the show air?",
                answer:
                  "New episodes of StarBite with Katarina air every Friday at 8 PM on PimPim. Episodes are also available for streaming immediately after broadcast.",
              },
              {
                question: "How can I be featured on the show?",
                answer:
                  "We're always looking for interesting guests and food establishments to feature. Please contact our production team through the contact form on this website.",
              },
              {
                question: "Does Katarina actually cook?",
                answer:
                  "Yes! Katarina is a trained chef with expertise in Nigerian cuisine. She often demonstrates cooking techniques and shares recipes on the show.",
              },
              {
                question: "Where is the show filmed?",
                answer:
                  "The show is primarily filmed in Lagos, Nigeria, with special episodes shot on location throughout the country to showcase regional cuisines.",
              },
            ].map((faq, i) => (
              <Card key={i} className="border-red-600/20">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-slate-800 mb-2">{faq.question}</h3>
                  <p className="text-slate-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
