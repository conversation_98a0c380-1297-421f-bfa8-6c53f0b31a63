"use client";

import { Episode } from "@/lib/data";
import { Calendar, Clock, Play } from "lucide-react";

interface EpisodeCardProps {
   episode: Episode;
}

export function EpisodeCard({ episode }: EpisodeCardProps) {
   return (
      <div key={episode.id} className="group cursor-pointer">
         <div className="bg-gray-100 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
            {/* Thumbnail */}
            <div className="relative h-48 bg-gradient-to-br from-[#FFD700]/20 to-[#DC2626]/20 flex items-center justify-center">
               <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Play
                     className="h-8 w-8 text-[#DC2626] ml-1"
                     fill="currentColor"
                  />
               </div>

               {/* Episode Info Overlay */}
               <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {`${
                     episode.id < 0 ? "Coming Soon" : `Episode ${episode.id}`
                  }`}
               </div>
            </div>

            {/* Content */}
            <div className="p-6">
               <div className="flex items-center justify-between mb-3">
                  <span className="text-[#DC2626] font-semibold text-sm">
                     {episode.category}
                  </span>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                     <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{episode.duration}</span>
                     </div>
                     {/* <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>
                           {new Date(episode.date).toLocaleDateString()}
                        </span>
                     </div> */}
                  </div>
               </div>

               <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-[#DC2626] transition-colors">
                  {episode.title}
               </h3>
               <h3 className="text-base font-bold text-[#DC2626] mb-2">
                  with {episode.guest}
               </h3>

               <p className="text-gray-600 leading-relaxed line-clamp-3">
                  {episode.description}
               </p>

               {episode.id <= 0 && (
                  <div className="space-y-2 pt-2">
                     <div className="w-full h-4 bg-gray-300 rounded-full"></div>
                     <div className="w-full h-4 bg-gray-300 rounded-full"></div>
                  </div>
               )}
            </div>
         </div>
      </div>
   );
}
